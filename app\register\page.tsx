"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import Image from "next/image";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_Checkbox from "../components/JC_Checkbox/JC_Checkbox";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Post } from "../apiServices/JC_Post";
import { signIn, useSession } from "next-auth/react";
import { UserModel } from "../models/User";
import { D_FieldModel_Email, D_FieldModel_FirstName, D_FieldModel_LastName, D_FieldModel_Phone } from "../models/ComponentModels/JC_Field";
import { FieldTypeEnum } from "../enums/FieldType";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { JC_Utils, JC_Utils_Validation } from "../Utils";



export default function Page_Register() {

    const session = useSession();

    // - STATE - //

    // Loading
    const [isLoading, setIsLoading] = useState<boolean>(false);
    // Error
    const [errorMessage, setErrorMessage] = useState<string>();
    // Register
    const [registerFirstName, setRegisterFirstName] = useState<string>("");
    const [registerLastName, setRegisterLastName] = useState<string>("");
    const [registerEmail, setRegisterEmail] = useState<string>("");

    const [registerPassword, setRegisterPassword] = useState<string>("");
    const [registerConfirmPassword, setRegisterConfirmPassword] = useState<string>("");
    const [emailPromotionsChecked, setEmailPromotionsChecked] = useState<boolean>(true);
    const [submitClicked, setSubmitClicked] = useState<boolean>(false);


    // - INITIALISE - //

    useEffect(() => {
        if (!JC_Utils.isOnMobile()) {
            (document.getElementById("register-first-input") as HTMLInputElement).select();
        }
    }, []);


    // - HANDLES - //

    async function register() {
        setIsLoading(true);
        setErrorMessage("");
        setSubmitClicked(true);
        try {
            let newUser:UserModel = new UserModel({
                FirstName: registerFirstName,
                LastName: registerLastName,
                Email: registerEmail,
                IsEmailSubscribed: emailPromotionsChecked
            });
            // Create User
            await JC_Put(
                UserModel,
                UserModel.apiRoute,
                newUser
            );
            // Send 'Welcome' email (if email API exists)
            // JC_Post("email/welcomeEmail", { name: `${newUser.FirstName} ${newUser.LastName}`, email: newUser.Email });
            // Login, then go back "Home"
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "1");
            const signInResult = await signIn("credentials", {
                email: registerEmail,
                redirect: false
            });

            if (signInResult?.error) {
                throw new Error(signInResult.error);
            }

            // Redirect to home page after successful login
            window.location.href = "/";
        } catch (error) {
            setErrorMessage((error as {message:string}).message);
            setIsLoading(false);
        }
    }


    // - Main - //

    return (
        <div className={styles.mainContainer}>
            <div className={styles.tabBody}>



                {/* Form */}
                <JC_Form
                    key={errorMessage}
                    submitButtonText="Register"
                    onSubmit={register}
                    isLoading={isLoading}
                    errorMessage={errorMessage}
                    fields={[
                        // First Name
                        {
                            ...D_FieldModel_FirstName(),
                            inputId:"register-first-input",
                            onChange: (newValue) => setRegisterFirstName(newValue),
                            value: registerFirstName
                        },
                        // Last Name
                        {
                            ...D_FieldModel_LastName(),
                            inputId:"register-last-name-input",
                            onChange: (newValue) => setRegisterLastName(newValue),
                            value: registerLastName
                        },
                        // Email
                        {
                            ...D_FieldModel_Email(),
                            inputId:"register-email-input",
                            onChange: (newValue) => setRegisterEmail(newValue),
                            value: registerEmail
                        },

                        // Password
                        {
                            inputId:"register-password-input",
                            type: FieldTypeEnum.Password,
                            label: "Password",
                            onChange: (newValue) => setRegisterPassword(newValue),
                            value: registerPassword,
                            validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                                    ? "Enter a password."
                                                    : !JC_Utils_Validation.validPassword(v)
                                                        ? `Password invalid.`
                                                        : ""
                        },

                        // Confirm Password
                        {
                            inputId:"register-confirm-password-input",
                            type: FieldTypeEnum.Password,
                            label: "Confirm Password",
                            onChange: (newValue) => setRegisterConfirmPassword(newValue),
                            value: registerConfirmPassword,
                            validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                                    ? "Confirm the password."
                                                    : registerConfirmPassword != registerPassword
                                                        ? "Passwords do not match"
                                                        : ""
                        },
                        // Email Promotions
                        {
                            overrideClass: styles.fieldOverride,
                            inputId: "email-promotions-checkbox",
                            type: FieldTypeEnum.Text,
                            customNode: <JC_Checkbox key="details-&-payment-title" label="Email Promotions" checked={emailPromotionsChecked} onChange={() => setEmailPromotionsChecked(!emailPromotionsChecked)} />
                        }
                    ]}
                />

            </div>
        </div>
    );
}
